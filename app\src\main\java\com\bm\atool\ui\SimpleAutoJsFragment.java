package com.bm.atool.ui;

import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.bm.atool.R;
import com.bm.atool.autojs.SimpleAutoJsManager;

/**
 * 简化的AutoJs脚本执行Fragment
 * 提供基本的JS脚本编辑和执行功能
 */
public class SimpleAutoJsFragment extends BaseFragment {
    private static final String TAG = "SimpleAutoJsFragment";
    
    private EditText etScriptContent;
    private EditText etScriptName;
    private Button btnExecuteSimpleScript;
    private Button btnExecuteComplexScript;
    private Button btnExecuteTestScript;
    private Button btnExecuteToutiaoScript;
    private Button btnExecuteWechatScript;
    private Button btnStopAllScripts;
    private Button btnCheckStatus;
    private TextView tvScriptStatus;
    private TextView tvRunningScripts;
    
    private SimpleAutoJsManager autoJsManager;
    private Handler uiHandler;
    
    public SimpleAutoJsFragment() {
        super();
        this.setTitle("JS脚本执行器");
    }
    
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView called");
        View view = inflater.inflate(R.layout.fragment_simple_autojs, container, false);

        initViews(view);
        initAutoJs();
        setupClickListeners();
        updateUI();

        Log.d(TAG, "Fragment initialization completed");

        // 移除自动执行，现在用户可以手动选择执行简单或复杂脚本

        return view;
    }
    
    private void initViews(View view) {
        etScriptContent = view.findViewById(R.id.etScriptContent);
        etScriptName = view.findViewById(R.id.etScriptName);
        btnExecuteSimpleScript = view.findViewById(R.id.btnExecuteSimpleScript);
        btnExecuteComplexScript = view.findViewById(R.id.btnExecuteComplexScript);
        btnExecuteTestScript = view.findViewById(R.id.btnExecuteTestScript);
        btnExecuteToutiaoScript = view.findViewById(R.id.btnExecuteToutiaoScript);
        btnExecuteWechatScript = view.findViewById(R.id.btnExecuteWechatScript);
        btnStopAllScripts = view.findViewById(R.id.btnStopAllScripts);
        btnCheckStatus = view.findViewById(R.id.btnCheckStatus);
        tvScriptStatus = view.findViewById(R.id.tvScriptStatus);
        tvRunningScripts = view.findViewById(R.id.tvRunningScripts);

        uiHandler = new Handler();

        // 设置默认脚本内容
        setDefaultScript();
    }
    
    private void initAutoJs() {
        autoJsManager = SimpleAutoJsManager.getInstance();
        if (!autoJsManager.isInitialized()) {
            Log.e(TAG, "SimpleAutoJsManager not initialized");
            showToast("JS引擎未初始化，请重启应用");
        }
    }
    
    private void setupClickListeners() {
        btnExecuteSimpleScript.setOnClickListener(v -> executeSimpleScript());
        btnExecuteComplexScript.setOnClickListener(v -> executeComplexScript());
        btnExecuteTestScript.setOnClickListener(v -> executeTestScript());
        btnExecuteToutiaoScript.setOnClickListener(v -> executeToutiaoScript());
        btnExecuteWechatScript.setOnClickListener(v -> executeWechatScript());
        btnStopAllScripts.setOnClickListener(v -> stopAllScripts());
        btnCheckStatus.setOnClickListener(v -> checkStatus());
    }
    
    private void setDefaultScript() {
        String defaultScript = "toast(\"Hello AutoJS!\");";

        etScriptContent.setText(defaultScript);
        etScriptName.setText("简单测试脚本");
    }
    
    private void executeSimpleScript() {
        Log.d(TAG, "executeSimpleScript button clicked");

        // 执行简单脚本：只显示toast
        String simpleScript = "toast(\"Hello AutoJS! 简单脚本执行成功！\");";
        String scriptName = "简单测试脚本";

        executeScriptInternal(simpleScript, scriptName);
    }

    private void executeComplexScript() {
        Log.d(TAG, "executeComplexScript button clicked");

        // 执行复杂脚本：测试多种功能
        String complexScript =
            "// 复杂脚本测试多种功能\n" +
            "console.log('开始执行复杂脚本测试...');\n" +
            "\n" +
            "// 1. 显示欢迎消息\n" +
            "toast('复杂脚本开始执行...');\n" +
            "\n" +
            "// 2. 延迟1秒\n" +
            "sleep(1000);\n" +
            "\n" +
            "// 3. 获取设备信息\n" +
            "console.log('设备宽度: ' + device.width);\n" +
            "console.log('设备高度: ' + device.height);\n" +
            "toast('设备尺寸: ' + device.width + 'x' + device.height);\n" +
            "\n" +
            "// 4. 再次延迟\n" +
            "sleep(1500);\n" +
            "\n" +
            "// 5. 获取当前包名\n" +
            "var packageName = currentPackage();\n" +
            "console.log('当前包名: ' + packageName);\n" +
            "toast('当前应用: ' + packageName);\n" +
            "\n" +
            "// 6. 延迟并显示完成消息\n" +
            "sleep(1000);\n" +
            "\n" +
            "// 7. 测试循环和计算\n" +
            "var sum = 0;\n" +
            "for (var i = 1; i <= 10; i++) {\n" +
            "    sum += i;\n" +
            "    console.log('计算中: ' + i + ', 当前和: ' + sum);\n" +
            "}\n" +
            "toast('1到10的和是: ' + sum);\n" +
            "\n" +
            "// 8. 最终完成消息\n" +
            "sleep(1000);\n" +
            "console.log('复杂脚本执行完成！');\n" +
            "toast('复杂脚本执行完成！所有功能测试通过！');";

        String scriptName = "复杂功能测试脚本";

        executeScriptInternal(complexScript, scriptName);
    }

    private void executeTestScript() {
        Log.d(TAG, "executeTestScript button clicked");

        // 执行测试脚本：打开Chrome，访问百度，搜索京东商城
        String testScript =
            "// 测试脚本：打开Chrome浏览器，访问百度，搜索京东商城\n" +
            "console.log('开始执行测试脚本：打开Chrome，访问百度，搜索京东商城');\n" +
            "\n" +
            "// 1. 显示开始消息\n" +
            "toast('开始执行测试脚本...');\n" +
            "sleep(1000);\n" +
            "\n" +
            "// 2. 打开百度网站\n" +
            "console.log('正在打开百度网站...');\n" +
            "toast('正在打开百度网站...');\n" +
            "openUrl('https://www.baidu.com');\n" +
            "sleep(3000);\n" +
            "\n" +
            "// 3. 显示提示信息\n" +
            "console.log('百度网站已在浏览器中打开');\n" +
            "toast('百度网站已在浏览器中打开！');\n" +
            "sleep(2000);\n" +
            "\n" +
            "// 4. 打开百度搜索京东的链接\n" +
            "console.log('正在打开百度搜索京东商城的结果页面...');\n" +
            "toast('正在搜索京东商城...');\n" +
            "openUrl('https://www.baidu.com/s?wd=京东商城');\n" +
            "sleep(2000);\n" +
            "\n" +
            "// 5. 显示完成消息\n" +
            "console.log('测试脚本执行完成！');\n" +
            "toast('测试脚本执行完成！\\n已完成：\\n1. 打开百度网站\\n2. 搜索京东商城');\n" +
            "\n" +
            "// 注意：浏览器已打开相关页面\n" +
            "console.log('浏览器已打开百度搜索京东商城的页面');";

        String scriptName = "测试脚本-百度搜索京东";

        executeScriptInternal(testScript, scriptName);
    }

    private void executeToutiaoScript() {
        Log.d(TAG, "executeToutiaoScript button clicked");

        // 执行今日头条脚本：打开今日头条app，模拟真实用户浏览行为
        String toutiaoScript =
            "// 今日头条智能浏览脚本：模拟真实用户行为\n" +
            "console.log('开始执行今日头条智能浏览脚本');\n" +
            "\n" +
            "// 工具函数：生成随机延迟时间，模拟人类操作\n" +
            "function humanDelay(min, max) {\n" +
            "    var delay = min + Math.floor(Math.random() * (max - min));\n" +
            "    return delay;\n" +
            "}\n" +
            "\n" +
            "// 工具函数：随机滑动，模拟真实浏览\n" +
            "function randomScroll() {\n" +
            "    var screenWidth = device.width;\n" +
            "    var screenHeight = device.height;\n" +
            "    var startX = screenWidth / 2 + Math.floor(Math.random() * 100 - 50);\n" +
            "    var startY = screenHeight * 0.7;\n" +
            "    var endY = screenHeight * 0.3;\n" +
            "    var duration = humanDelay(300, 800);\n" +
            "    \n" +
            "    swipe(startX, startY, startX, endY, duration);\n" +
            "    console.log('执行随机滑动，持续时间: ' + duration + 'ms');\n" +
            "}\n" +
            "\n" +
            "// 工具函数：随机点击屏幕上的内容\n" +
            "function randomClick() {\n" +
            "    var screenWidth = device.width;\n" +
            "    var screenHeight = device.height;\n" +
            "    \n" +
            "    // 避开顶部状态栏和底部导航栏的安全区域\n" +
            "    var safeTop = screenHeight * 0.15;\n" +
            "    var safeBottom = screenHeight * 0.85;\n" +
            "    var safeLeft = screenWidth * 0.1;\n" +
            "    var safeRight = screenWidth * 0.9;\n" +
            "    \n" +
            "    var clickX = safeLeft + Math.floor(Math.random() * (safeRight - safeLeft));\n" +
            "    var clickY = safeTop + Math.floor(Math.random() * (safeBottom - safeTop));\n" +
            "    \n" +
            "    click(clickX, clickY);\n" +
            "    console.log('随机点击位置: (' + clickX + ', ' + clickY + ')');\n" +
            "}\n" +
            "\n" +
            "// 工具函数：点击底部导航栏\n" +
            "function clickBottomNavigation() {\n" +
            "    var screenWidth = device.width;\n" +
            "    var screenHeight = device.height;\n" +
            "    \n" +
            "    // 底部导航栏通常在屏幕底部\n" +
            "    var navY = screenHeight * 0.95;\n" +
            "    \n" +
            "    // 常见的底部导航位置：首页、视频、商城、我的\n" +
            "    var navPositions = [\n" +
            "        screenWidth * 0.125,  // 首页\n" +
            "        screenWidth * 0.375,  // 视频\n" +
            "        screenWidth * 0.625,  // 商城\n" +
            "        screenWidth * 0.875   // 我的\n" +
            "    ];\n" +
            "    \n" +
            "    var randomNav = navPositions[Math.floor(Math.random() * navPositions.length)];\n" +
            "    click(randomNav, navY);\n" +
            "    console.log('点击底部导航栏位置: (' + randomNav + ', ' + navY + ')');\n" +
            "    \n" +
            "    // 等待页面加载\n" +
            "    sleep(humanDelay(1500, 3000));\n" +
            "}\n" +
            "\n" +
            "// 工具函数：模拟阅读文章详情\n" +
            "function readArticleDetail() {\n" +
            "    console.log('进入文章详情页面，开始阅读...');\n" +
            "    \n" +
            "    // 模拟阅读时间\n" +
            "    var readingTime = humanDelay(8000, 20000);\n" +
            "    var scrollCount = Math.floor(readingTime / 3000);\n" +
            "    \n" +
            "    for (var i = 0; i < scrollCount; i++) {\n" +
            "        sleep(humanDelay(2000, 4000));\n" +
            "        randomScroll();\n" +
            "        \n" +
            "        // 偶尔停顿，模拟仔细阅读\n" +
            "        if (Math.random() < 0.3) {\n" +
            "            sleep(humanDelay(3000, 6000));\n" +
            "            console.log('仔细阅读中...');\n" +
            "        }\n" +
            "    }\n" +
            "    \n" +
            "    // 返回上一页\n" +
            "    back();\n" +
            "    console.log('阅读完成，返回列表页');\n" +
            "    sleep(humanDelay(1000, 2000));\n" +
            "}\n" +
            "\n" +
            "// 1. 显示开始消息\n" +
            "toast('开始执行今日头条智能浏览脚本...');\n" +
            "sleep(1000);\n" +
            "\n" +
            "// 2. 尝试启动今日头条app\n" +
            "console.log('正在启动今日头条app...');\n" +
            "toast('正在启动今日头条app...');\n" +
            "\n" +
            "// 今日头条的可能包名列表\n" +
            "var possiblePackages = [\n" +
            "    'com.ss.android.article.news',\n" +
            "    'com.ss.android.article.lite',\n" +
            "    'com.bytedance.news.lite'\n" +
            "];\n" +
            "\n" +
            "var toutiaoPackage = null;\n" +
            "var launched = false;\n" +
            "\n" +
            "// 首先检查哪个包名已安装\n" +
            "console.log('检查今日头条app是否已安装...');\n" +
            "for (var i = 0; i < possiblePackages.length; i++) {\n" +
            "    var pkg = possiblePackages[i];\n" +
            "    console.log('检查包名: ' + pkg);\n" +
            "    if (isAppInstalled(pkg)) {\n" +
            "        console.log('找到已安装的今日头条: ' + pkg);\n" +
            "        toutiaoPackage = pkg;\n" +
            "        break;\n" +
            "    }\n" +
            "}\n" +
            "\n" +
            "if (toutiaoPackage === null) {\n" +
            "    console.log('未找到任何已安装的今日头条app');\n" +
            "    toast('今日头条app未安装，请先安装今日头条');\n" +
            "} else {\n" +
            "    console.log('尝试启动今日头条: ' + toutiaoPackage);\n" +
            "    launched = launchApp(toutiaoPackage);\n" +
            "    \n" +
            "    if (!launched) {\n" +
            "        console.log('启动失败，尝试等待后重试...');\n" +
            "        sleep(2000);\n" +
            "        launched = launchApp(toutiaoPackage);\n" +
            "    }\n" +
            "}\n" +
            "\n" +
            "if (!launched) {\n" +
            "    console.log('无法启动今日头条app，可能未安装');\n" +
            "    toast('今日头条app未安装，脚本结束');\n" +
            "} else {\n" +
            "    console.log('今日头条app启动成功: ' + toutiaoPackage);\n" +
            "    toast('今日头条app启动成功！开始智能浏览...');\n" +
            "    sleep(humanDelay(3000, 5000));\n" +
            "    \n" +
            "    // 3. 开始智能浏览模式（3分钟）\n" +
            "    console.log('开始智能浏览模式，持续3分钟...');\n" +
            "    \n" +
            "    var startTime = Date.now();\n" +
            "    var duration = 3 * 60 * 1000; // 3分钟\n" +
            "    var actionCount = 0;\n" +
            "    var articlesRead = 0;\n" +
            "    \n" +
            "    while (Date.now() - startTime < duration) {\n" +
            "        actionCount++;\n" +
            "        var remainingTime = Math.ceil((duration - (Date.now() - startTime)) / 1000);\n" +
            "        \n" +
            "        console.log('智能操作 #' + actionCount + '，剩余时间: ' + remainingTime + '秒');\n" +
            "        \n" +
            "        // 检查是否还在今日头条app中\n" +
            "        var currentApp = currentPackage();\n" +
            "        if (currentApp !== toutiaoPackage) {\n" +
            "            console.log('检测到已离开今日头条app，当前app: ' + currentApp);\n" +
            "            console.log('尝试重新启动今日头条app...');\n" +
            "            if (!launchApp(toutiaoPackage)) {\n" +
            "                console.log('无法重新启动今日头条app，脚本结束');\n" +
            "                break;\n" +
            "            }\n" +
            "            sleep(humanDelay(2000, 3000));\n" +
            "            continue;\n" +
            "        }\n" +
            "        \n" +
            "        // 随机选择操作类型\n" +
            "        var actionType = Math.random();\n" +
            "        \n" +
            "        if (actionType < 0.4) {\n" +
            "            // 40% 概率：随机滑动浏览\n" +
            "            console.log('执行随机滑动浏览');\n" +
            "            randomScroll();\n" +
            "            sleep(humanDelay(2000, 4000));\n" +
            "            \n" +
            "        } else if (actionType < 0.7) {\n" +
            "            // 30% 概率：随机点击内容\n" +
            "            console.log('随机点击内容');\n" +
            "            randomClick();\n" +
            "            sleep(humanDelay(1000, 2000));\n" +
            "            \n" +
            "            // 如果点击进入了文章详情，进行阅读\n" +
            "            if (Math.random() < 0.6) {\n" +
            "                articlesRead++;\n" +
            "                readArticleDetail();\n" +
            "            }\n" +
            "            \n" +
            "        } else {\n" +
            "            // 30% 概率：切换底部导航栏\n" +
            "            console.log('切换底部导航栏');\n" +
            "            clickBottomNavigation();\n" +
            "            \n" +
            "            // 在新页面浏览一会\n" +
            "            for (var j = 0; j < 2; j++) {\n" +
            "                sleep(humanDelay(2000, 4000));\n" +
            "                randomScroll();\n" +
            "            }\n" +
            "        }\n" +
            "        \n" +
            "        // 每分钟显示一次进度提示\n" +
            "        if (actionCount % 10 === 0) {\n" +
            "            toast('智能浏览中...\\n已阅读 ' + articlesRead + ' 篇文章\\n剩余时间: ' + Math.floor(remainingTime / 60) + '分' + (remainingTime % 60) + '秒');\n" +
            "        }\n" +
            "        \n" +
            "        // 随机暂停，模拟真实用户行为\n" +
            "        if (Math.random() < 0.1) {\n" +
            "            var pauseTime = humanDelay(3000, 8000);\n" +
            "            console.log('模拟用户暂停思考，暂停 ' + pauseTime + 'ms');\n" +
            "            sleep(pauseTime);\n" +
            "        }\n" +
            "    }\n" +
            "    \n" +
            "    // 4. 显示完成消息\n" +
            "    console.log('今日头条智能浏览脚本执行完成！');\n" +
            "    console.log('总共执行了 ' + actionCount + ' 次智能操作');\n" +
            "    console.log('阅读了 ' + articlesRead + ' 篇文章');\n" +
            "    toast('今日头条智能浏览完成！\\n执行了 ' + actionCount + ' 次操作\\n阅读了 ' + articlesRead + ' 篇文章\\n持续时间: 3分钟');\n" +
            "    \n" +
            "    console.log('脚本执行结束，感谢使用！');\n" +
            "}";

        String scriptName = "今日头条智能浏览脚本";

        executeScriptInternal(toutiaoScript, scriptName);
    }

    private void executeWechatScript() {
        Log.d(TAG, "executeWechatScript button clicked");

        // 执行微信朋友圈脚本：启动微信→发现→朋友圈（简化演示版本）
        String wechatScript =
            "// 微信朋友圈测试脚本：启动微信→发现→朋友圈（简化演示版本）\n" +
            "console.log('开始执行微信朋友圈测试脚本');\n" +
            "\n" +
            "// 工具函数：生成随机延迟时间，模拟人类操作\n" +
            "function humanDelay(min, max) {\n" +
            "    var delay = min + Math.floor(Math.random() * (max - min));\n" +
            "    return delay;\n" +
            "}\n" +
            "\n" +
            "// 注意：当前JS环境不支持UI自动化函数，此脚本仅演示流程\n" +
            "console.log('⚠️ 注意：当前环境不支持UI自动化操作');\n" +
            "console.log('此脚本仅演示微信朋友圈导航的完整流程');\n" +
            "\n" +
            "// 1. 显示开始消息\n" +
            "toast('开始执行微信朋友圈测试脚本...');\n" +
            "sleep(1000);\n" +
            "\n" +
            "// 2. 尝试启动微信app\n" +
            "console.log('正在启动微信app...');\n" +
            "toast('正在启动微信app...');\n" +
            "\n" +
            "// 微信的包名\n" +
            "var wechatPackage = 'com.tencent.mm';\n" +
            "var launched = false;\n" +
            "\n" +
            "// 检查微信是否已安装\n" +
            "console.log('检查微信app是否已安装...');\n" +
            "if (isAppInstalled(wechatPackage)) {\n" +
            "    console.log('找到已安装的微信app');\n" +
            "    launched = launchApp(wechatPackage);\n" +
            "    \n" +
            "    if (!launched) {\n" +
            "        console.log('启动失败，尝试等待后重试...');\n" +
            "        sleep(2000);\n" +
            "        launched = launchApp(wechatPackage);\n" +
            "    }\n" +
            "} else {\n" +
            "    console.log('未找到微信app');\n" +
            "    toast('微信app未安装，请先安装微信');\n" +
            "}\n" +
            "\n" +
            "if (!launched) {\n" +
            "    console.log('无法启动微信app，可能未安装');\n" +
            "    toast('微信app未安装，脚本结束');\n" +
            "} else {\n" +
            "    console.log('微信app启动成功');\n" +
            "    toast('微信app启动成功！');\n" +
            "    sleep(humanDelay(3000, 5000));\n" +
            "    \n" +
            "    // 3. 模拟导航流程（演示版本）\n" +
            "    console.log('步骤1: 模拟点击底部发现标签');\n" +
            "    toast('步骤1: 模拟点击发现标签');\n" +
            "    \n" +
            "    // 计算发现标签的坐标位置\n" +
            "    var screenWidth = device.width;\n" +
            "    var screenHeight = device.height;\n" +
            "    var discoverX = Math.floor(screenWidth * 0.625); // 发现标签位置\n" +
            "    var discoverY = Math.floor(screenHeight * 0.95);  // 底部导航栏\n" +
            "    \n" +
            "    console.log('模拟点击发现标签位置: (' + discoverX + ', ' + discoverY + ')');\n" +
            "    console.log('屏幕尺寸: ' + screenWidth + 'x' + screenHeight);\n" +
            "    sleep(humanDelay(2000, 3000));\n" +
            "    \n" +
            "    // 4. 模拟点击朋友圈入口\n" +
            "    console.log('步骤2: 模拟点击朋友圈入口');\n" +
            "    toast('步骤2: 模拟点击朋友圈');\n" +
            "    \n" +
            "    // 计算朋友圈入口的坐标位置\n" +
            "    var momentsX = Math.floor(screenWidth * 0.5);   // 屏幕中央\n" +
            "    var momentsY = Math.floor(screenHeight * 0.2);  // 页面上方\n" +
            "    \n" +
            "    console.log('模拟点击朋友圈入口位置: (' + momentsX + ', ' + momentsY + ')');\n" +
            "    sleep(humanDelay(2000, 3000));\n" +
            "    \n" +
            "    // 5. 验证当前应用状态\n" +
            "    console.log('步骤3: 验证当前应用状态');\n" +
            "    var currentApp = currentPackage();\n" +
            "    console.log('当前应用包名: ' + currentApp);\n" +
            "    \n" +
            "    if (currentApp === wechatPackage) {\n" +
            "        console.log('✅ 当前仍在微信app中，导航流程演示成功');\n" +
            "        toast('✅ 微信朋友圈导航演示成功！\\n当前在微信应用中');\n" +
            "    } else {\n" +
            "        console.log('⚠️ 当前不在微信app中: ' + currentApp);\n" +
            "        toast('⚠️ 当前不在微信中\\n可能需要手动操作');\n" +
            "    }\n" +
            "    \n" +
            "    // 6. 显示完成消息\n" +
            "    console.log('微信朋友圈测试脚本执行完成！');\n" +
            "    console.log('注意：这是演示版本，实际UI操作需要无障碍服务支持');\n" +
            "    toast('微信朋友圈测试完成！\\n演示脚本执行结束');\n" +
            "}\n" +
            "\n" +
            "console.log('脚本执行结束，感谢使用！');";

        String scriptName = "微信朋友圈测试脚本";

        executeScriptInternal(wechatScript, scriptName);
    }

    private void executeScriptInternal(String scriptContent, String scriptName) {
        Log.d(TAG, "executeScriptInternal called for: " + scriptName);
        Log.d(TAG, "Script name: " + scriptName + ", content length: " + scriptContent.length());

        if (TextUtils.isEmpty(scriptContent)) {
            showToast("脚本内容为空");
            return;
        }
        
        if (!autoJsManager.isInitialized()) {
            showToast("JS引擎未初始化");
            return;
        }
        
        try {
            // 执行脚本
            String scriptId = autoJsManager.executeScript(scriptContent, scriptName, 
                new SimpleAutoJsManager.ScriptExecutionCallback() {
                    @Override
                    public void onStart(String scriptName) {
                        uiHandler.post(() -> {
                            tvScriptStatus.setText("脚本正在执行: " + scriptName);
                            updateRunningScriptsCount();
                        });
                    }
                    
                    @Override
                    public void onSuccess(String scriptName, String result) {
                        uiHandler.post(() -> {
                            tvScriptStatus.setText("脚本执行成功: " + scriptName + " - " + result);
                            updateRunningScriptsCount();
                            showToast("脚本执行成功");
                        });
                    }
                    
                    @Override
                    public void onError(String scriptName, String error) {
                        uiHandler.post(() -> {
                            // 截取错误信息的前200个字符显示在UI上，避免文本过长
                            String errorPreview = error.length() > 200 ? error.substring(0, 200) + "..." : error;
                            tvScriptStatus.setText("脚本执行失败: " + scriptName + " - " + errorPreview);
                            updateRunningScriptsCount();
                            showToast("脚本执行失败: " + errorPreview);
                        });
                    }
                });
            
            if (scriptId != null) {
                Log.d(TAG, "Script execution started: " + scriptName + " (ID: " + scriptId + ")");
                showToast("脚本开始执行");
                updateRunningScriptsCount();
            } else {
                showToast("脚本执行失败");
                tvScriptStatus.setText("脚本执行失败");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error executing script", e);
            showToast("脚本执行出错: " + e.getMessage());
            tvScriptStatus.setText("脚本执行出错: " + e.getMessage());
        }
    }
    
    private void stopAllScripts() {
        try {
            autoJsManager.stopAllScripts();
            showToast("已停止所有脚本");
            tvScriptStatus.setText("已停止所有脚本");
            updateRunningScriptsCount();
        } catch (Exception e) {
            Log.e(TAG, "Error stopping scripts", e);
            showToast("停止脚本失败: " + e.getMessage());
        }
    }
    
    private void checkStatus() {
        if (!autoJsManager.isInitialized()) {
            showToast("JS引擎未初始化");
            return;
        }
        
        int runningCount = autoJsManager.getRunningScriptCount();
        String status = "JS引擎状态: 正常\n运行中的脚本: " + runningCount;
        
        showToast("JS引擎运行正常");
        tvScriptStatus.setText(status);
        updateRunningScriptsCount();
    }
    
    private void updateRunningScriptsCount() {
        try {
            int count = autoJsManager.getRunningScriptCount();
            tvRunningScripts.setText("运行中的脚本: " + count);
        } catch (Exception e) {
            Log.e(TAG, "Error getting running script count", e);
            tvRunningScripts.setText("运行中的脚本: 未知");
        }
    }
    
    private void updateUI() {
        updateRunningScriptsCount();
        
        if (autoJsManager.isInitialized()) {
            tvScriptStatus.setText("JS引擎已就绪");
        } else {
            tvScriptStatus.setText("JS引擎未初始化");
        }
    }
    
    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        updateUI();
    }
    
    @Override
    public int getIconResourceId() {
        return R.drawable.ic_code;
    }
}
