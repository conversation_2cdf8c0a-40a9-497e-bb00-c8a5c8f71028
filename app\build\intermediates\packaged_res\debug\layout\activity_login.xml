<?xml version="1.0" encoding="utf-8"?>
<android.widget.LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:background="#ffffff"
    android:padding="30sp"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="10dp"
        android:textAlignment="center"
        android:textColor="#2c2c2c"
        android:textSize="24sp"
        tools:text="Android Tool" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10sp"
        android:padding="10dp"
        android:text="User Name"
        android:textColor="#2c2c2c"
        android:textSize="14sp" />
    <EditText
        android:textSize="18sp"
        android:textColorHint="#a5b7c6"
        android:id="@+id/et_account"
        android:background="#f6f8fa"
        android:padding="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:hint="Login ID"
        android:singleLine="true"
        android:imeOptions="actionNext"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10sp"
        android:padding="10dp"
        android:text="Password"
        android:textColor="#2c2c2c"
        android:textSize="14sp" />
    <EditText
        android:textSize="18sp"
        android:textColorHint="#a5b7c6"
        android:id="@+id/et_password"
        android:background="#f6f8fa"
        android:padding="10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="5dp"
        android:hint="Password"
        android:singleLine="true"
        android:inputType="textPassword"
        android:imeOptions="actionDone"/>

    <Button
        android:id="@+id/btn_Login"
        android:layout_width="match_parent"
        android:layout_height="56sp"
        android:layout_marginTop="25dp"
        android:backgroundTint="#FFC107"
        android:text="Login"
        android:textColor="@android:color/white"
        android:textSize="20sp" />

</android.widget.LinearLayout>