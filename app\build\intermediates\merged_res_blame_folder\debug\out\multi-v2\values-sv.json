{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\34b7d14a8e52fea8318500861032b192\\transformed\\material-1.5.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,221,302,400,522,601,664,756,820,880,972,1035,1097,1164,1228,1282,1387,1446,1507,1561,1630,1749,1832,1916,2022,2101,2185,2271,2338,2404,2473,2547,2636,2708,2785,2856,2930,3021,3100,3187,3275,3347,3421,3506,3557,3624,3705,3789,3851,3915,3978,4085,4192,4291,4399", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,80,97,121,78,62,91,63,59,91,62,61,66,63,53,104,58,60,53,68,118,82,83,105,78,83,85,66,65,68,73,88,71,76,70,73,90,78,86,87,71,73,84,50,66,80,83,61,63,62,106,106,98,107,77", "endOffsets": "216,297,395,517,596,659,751,815,875,967,1030,1092,1159,1223,1277,1382,1441,1502,1556,1625,1744,1827,1911,2017,2096,2180,2266,2333,2399,2468,2542,2631,2703,2780,2851,2925,3016,3095,3182,3270,3342,3416,3501,3552,3619,3700,3784,3846,3910,3973,4080,4187,4286,4394,4472"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2942,3751,3849,3971,4121,4272,4364,4428,4488,4580,4643,4705,4772,4836,4890,4995,5054,5115,5169,5238,5357,5440,5524,5630,5709,5793,5879,5946,6012,6081,6155,6244,6316,6393,6464,6538,6629,6708,6795,6883,6955,7029,7114,7165,7232,7313,7397,7459,7523,7586,7693,7800,7899,8313", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,80,97,121,78,62,91,63,59,91,62,61,66,63,53,104,58,60,53,68,118,82,83,105,78,83,85,66,65,68,73,88,71,76,70,73,90,78,86,87,71,73,84,50,66,80,83,61,63,62,106,106,98,107,77", "endOffsets": "266,3018,3844,3966,4045,4179,4359,4423,4483,4575,4638,4700,4767,4831,4885,4990,5049,5110,5164,5233,5352,5435,5519,5625,5704,5788,5874,5941,6007,6076,6150,6239,6311,6388,6459,6533,6624,6703,6790,6878,6950,7024,7109,7160,7227,7308,7392,7454,7518,7581,7688,7795,7894,8002,8386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\727e0e520560e457a55a364584866f29\\transformed\\navigation-ui-2.7.7\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,118", "endOffsets": "159,278"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8007,8116", "endColumns": "108,118", "endOffsets": "8111,8230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7aaeb8978a33c50dcbceb6613d676e40\\transformed\\preference-1.2.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,342,479,648,727", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "171,259,337,474,643,722,798"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4050,4184,8235,8391,8709,8878,8957", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "4116,4267,8308,8523,8873,8952,9028"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\386f0edc44f6b3acaac787bafbf75b36\\transformed\\core-1.13.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3023,3118,3220,3318,3417,3525,3630,8608", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3113,3215,3313,3412,3520,3625,3746,8704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc75f59f79e9064a41f591ed3352b14a\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "271,374,477,588,672,772,885,962,1037,1130,1225,1320,1414,1516,1611,1708,1806,1902,1995,2075,2181,2280,2376,2481,2584,2686,2840,8528", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "369,472,583,667,767,880,957,1032,1125,1220,1315,1409,1511,1606,1703,1801,1897,1990,2070,2176,2275,2371,2476,2579,2681,2835,2937,8603"}}]}]}