<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_autojs" modulePackage="com.bm.atool" filePath="app\src\main\res\layout\fragment_autojs.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/fragment_autojs_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="151" endOffset="12"/></Target><Target id="@+id/tvScriptStatus" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="37" endOffset="51"/></Target><Target id="@+id/tvRunningScripts" view="TextView"><Expressions/><location startLine="39" startOffset="12" endLine="44" endOffset="41"/></Target><Target id="@+id/etScriptName" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="56" startOffset="12" endLine="60" endOffset="43"/></Target><Target id="@+id/etScriptContent" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="72" startOffset="12" endLine="80" endOffset="41"/></Target><Target id="@+id/btnExecuteScript" view="Button"><Expressions/><location startLine="91" startOffset="12" endLine="99" endOffset="40"/></Target><Target id="@+id/btnStopAllScripts" view="Button"><Expressions/><location startLine="106" startOffset="16" endLine="115" endOffset="44"/></Target><Target id="@+id/btnCheckPermissions" view="Button"><Expressions/><location startLine="117" startOffset="16" endLine="125" endOffset="44"/></Target></Targets></Layout>