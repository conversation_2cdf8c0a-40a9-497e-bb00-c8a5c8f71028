{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\34b7d14a8e52fea8318500861032b192\\transformed\\material-1.5.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,303,409,525,608,673,767,832,891,978,1040,1100,1166,1228,1282,1394,1451,1512,1566,1638,1764,1850,1934,2043,2124,2205,2295,2362,2428,2500,2584,2667,2742,2818,2891,2966,3051,3126,3218,3312,3386,3459,3553,3605,3674,3759,3846,3908,3972,4035,4138,4238,4333,4435", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "219,298,404,520,603,668,762,827,886,973,1035,1095,1161,1223,1277,1389,1446,1507,1561,1633,1759,1845,1929,2038,2119,2200,2290,2357,2423,2495,2579,2662,2737,2813,2886,2961,3046,3121,3213,3307,3381,3454,3548,3600,3669,3754,3841,3903,3967,4030,4133,4233,4328,4430,4510"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2981,3790,3896,4012,4164,4316,4410,4475,4534,4621,4683,4743,4809,4871,4925,5037,5094,5155,5209,5281,5407,5493,5577,5686,5767,5848,5938,6005,6071,6143,6227,6310,6385,6461,6534,6609,6694,6769,6861,6955,7029,7102,7196,7248,7317,7402,7489,7551,7615,7678,7781,7881,7976,8382", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,78,105,115,82,64,93,64,58,86,61,59,65,61,53,111,56,60,53,71,125,85,83,108,80,80,89,66,65,71,83,82,74,75,72,74,84,74,91,93,73,72,93,51,68,84,86,61,63,62,102,99,94,101,79", "endOffsets": "269,3055,3891,4007,4090,4224,4405,4470,4529,4616,4678,4738,4804,4866,4920,5032,5089,5150,5204,5276,5402,5488,5572,5681,5762,5843,5933,6000,6066,6138,6222,6305,6380,6456,6529,6604,6689,6764,6856,6950,7024,7097,7191,7243,7312,7397,7484,7546,7610,7673,7776,7876,7971,8073,8457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc75f59f79e9064a41f591ed3352b14a\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,389,493,601,688,792,908,991,1069,1160,1253,1348,1442,1542,1635,1730,1824,1915,2006,2092,2195,2300,2401,2505,2614,2722,2882,8598", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "384,488,596,683,787,903,986,1064,1155,1248,1343,1437,1537,1630,1725,1819,1910,2001,2087,2190,2295,2396,2500,2609,2717,2877,2976,8678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\386f0edc44f6b3acaac787bafbf75b36\\transformed\\core-1.13.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3060,3155,3257,3354,3451,3557,3675,8683", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3150,3252,3349,3446,3552,3670,3785,8779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7aaeb8978a33c50dcbceb6613d676e40\\transformed\\preference-1.2.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,261,341,477,646,731", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "169,256,336,472,641,726,805"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4095,4229,8302,8462,8784,8953,9038", "endColumns": "68,86,79,135,168,84,78", "endOffsets": "4159,4311,8377,8593,8948,9033,9112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\727e0e520560e457a55a364584866f29\\transformed\\navigation-ui-2.7.7\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,113", "endOffsets": "160,274"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8078,8188", "endColumns": "109,113", "endOffsets": "8183,8297"}}]}]}