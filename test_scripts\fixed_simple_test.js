// 简单测试脚本 - 修复版
console.log("简单测试脚本开始执行");

// 添加一个小延迟确保API完全加载
setTimeout(function() {
    try {
        // 测试基本功能
        toast("Hello from JavaScript!");
        console.log("Toast功能正常");
        
        // 获取当前应用包名
        var pkg = currentPackage();
        console.log("当前应用包名: " + pkg);
        
        // 获取设备信息
        console.log("设备宽度: " + device.width);
        console.log("设备高度: " + device.height);
        
        // 测试基本计算
        var result = 10 + 20;
        console.log("计算结果: 10 + 20 = " + result);
        
        // 最终提示
        toast("简单测试完成!");
        console.log("简单测试脚本执行完成");
        
        // 返回结果
        return "简单测试完成";
    } catch (e) {
        console.error("执行出错: " + e.message);
        toast("执行出错: " + e.message);
    }
}, 500); // 延迟500ms确保API加载完成