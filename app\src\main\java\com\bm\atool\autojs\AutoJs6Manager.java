package com.bm.atool.autojs;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import org.autojs.autojs.AutoJs;
import org.autojs.autojs.engine.ScriptEngineService;
import org.autojs.autojs.execution.ExecutionConfig;
import org.autojs.autojs.execution.ScriptExecution;
import org.autojs.autojs.execution.ScriptExecutionListener;
import org.autojs.autojs.script.JavaScriptSource;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * AutoJs6管理器
 * 使用AutoJs6引擎提供完整的JavaScript脚本执行功能
 */
public class AutoJs6Manager {
    private static final String TAG = "AutoJs6Manager";
    
    private static AutoJs6Manager instance;
    private Context context;
    private boolean isInitialized = false;
    private ConcurrentHashMap<String, ScriptExecution> runningScripts;
    private AtomicInteger scriptCounter;
    private ScriptEngineService scriptEngineService;
    
    private AutoJs6Manager() {
        runningScripts = new ConcurrentHashMap<>();
        scriptCounter = new AtomicInteger(0);
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized AutoJs6Manager getInstance() {
        if (instance == null) {
            instance = new AutoJs6Manager();
        }
        return instance;
    }
    
    /**
     * 初始化管理器
     */
    public boolean initialize(Application application) {
        if (isInitialized) {
            Log.d(TAG, "AutoJs6Manager already initialized");
            return true;
        }
        
        try {
            this.context = application.getApplicationContext();
            
            // 初始化AutoJs6
            AutoJs.initInstance(application);
            scriptEngineService = AutoJs.getInstance().getScriptEngineService();
            
            isInitialized = true;
            Log.d(TAG, "AutoJs6Manager initialized successfully");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize AutoJs6Manager", e);
            return false;
        }
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized && scriptEngineService != null;
    }
    
    /**
     * 执行JavaScript脚本
     */
    public String executeScript(String scriptContent, String scriptName, ScriptExecutionCallback callback) {
        if (!isInitialized()) {
            Log.e(TAG, "AutoJs6Manager not initialized");
            if (callback != null) {
                callback.onError(scriptName, "AutoJs6Manager not initialized");
            }
            return null;
        }
        
        try {
            // 生成唯一的脚本ID
            String scriptId = scriptName + "_" + scriptCounter.incrementAndGet();
            
            // 创建JavaScript源码
            JavaScriptSource source = new JavaScriptSource(scriptName, scriptContent);
            
            // 设置执行模式，启用自动化功能
            source.setExecutionMode(JavaScriptSource.EXECUTION_MODE_AUTO);
            
            // 创建执行配置
            ExecutionConfig config = new ExecutionConfig();
            config.setWorkingDirectory(context.getFilesDir().getAbsolutePath());
            
            // 创建执行监听器
            ScriptExecutionListener listener = new ScriptExecutionListener() {
                @Override
                public void onStart(ScriptExecution execution) {
                    Log.d(TAG, "Script started: " + scriptName);
                    if (callback != null) {
                        callback.onStart(scriptName);
                    }
                }
                
                @Override
                public void onSuccess(ScriptExecution execution, Object result) {
                    Log.d(TAG, "Script completed successfully: " + scriptName);
                    runningScripts.remove(scriptId);
                    if (callback != null) {
                        callback.onSuccess(scriptName, result != null ? result.toString() : "Success");
                    }
                }
                
                @Override
                public void onException(ScriptExecution execution, Throwable e) {
                    Log.e(TAG, "Script execution failed: " + scriptName, e);
                    runningScripts.remove(scriptId);
                    if (callback != null) {
                        callback.onError(scriptName, e.getMessage());
                    }
                }
            };
            
            // 执行脚本
            ScriptExecution execution = scriptEngineService.execute(source, listener, config);
            runningScripts.put(scriptId, execution);
            
            Log.d(TAG, "Script execution started: " + scriptName + " (ID: " + scriptId + ")");
            return scriptId;
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to execute script: " + scriptName, e);
            if (callback != null) {
                callback.onError(scriptName, e.getMessage());
            }
            return null;
        }
    }
    
    /**
     * 停止指定脚本
     */
    public boolean stopScript(String scriptId) {
        if (!isInitialized()) {
            Log.e(TAG, "AutoJs6Manager not initialized");
            return false;
        }
        
        ScriptExecution execution = runningScripts.get(scriptId);
        if (execution != null) {
            try {
                execution.getEngine().forceStop();
                runningScripts.remove(scriptId);
                Log.d(TAG, "Script stopped: " + scriptId);
                return true;
            } catch (Exception e) {
                Log.e(TAG, "Failed to stop script: " + scriptId, e);
                return false;
            }
        } else {
            Log.w(TAG, "Script not found: " + scriptId);
            return false;
        }
    }
    
    /**
     * 停止所有脚本
     */
    public int stopAllScripts() {
        if (!isInitialized()) {
            Log.e(TAG, "AutoJs6Manager not initialized");
            return 0;
        }
        
        try {
            int stoppedCount = scriptEngineService.stopAll();
            runningScripts.clear();
            Log.d(TAG, "Stopped " + stoppedCount + " scripts");
            return stoppedCount;
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop all scripts", e);
            return 0;
        }
    }
    
    /**
     * 获取正在运行的脚本数量
     */
    public int getRunningScriptCount() {
        return runningScripts.size();
    }
    
    /**
     * 检查无障碍服务是否启用
     */
    public boolean isAccessibilityServiceEnabled() {
        if (!isInitialized()) {
            return false;
        }
        
        try {
            return AutoJs.getInstance().getAccessibilityBridge().getService() != null;
        } catch (Exception e) {
            Log.e(TAG, "Failed to check accessibility service status", e);
            return false;
        }
    }
    
    /**
     * 启用无障碍服务
     */
    public void enableAccessibilityService() {
        if (!isInitialized()) {
            Log.e(TAG, "AutoJs6Manager not initialized");
            return;
        }
        
        try {
            AutoJs.getInstance().getAccessibilityBridge().ensureServiceStarted();
            Log.d(TAG, "Accessibility service enabled");
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable accessibility service", e);
        }
    }
    
    /**
     * 脚本执行回调接口
     */
    public interface ScriptExecutionCallback {
        void onStart(String scriptName);
        void onSuccess(String scriptName, String result);
        void onError(String scriptName, String error);
    }
}
