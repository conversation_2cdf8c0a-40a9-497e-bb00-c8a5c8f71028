{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\34b7d14a8e52fea8318500861032b192\\transformed\\material-1.5.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,279,360,461,595,678,743,837,910,971,1096,1164,1225,1297,1357,1411,1531,1591,1653,1707,1784,1914,2001,2083,2194,2274,2359,2450,2517,2583,2657,2738,2822,2895,2972,3049,3123,3216,3291,3381,3472,3544,3622,3713,3767,3835,3919,4006,4068,4132,4195,4305,4418,4521,4633", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,80,100,133,82,64,93,72,60,124,67,60,71,59,53,119,59,61,53,76,129,86,81,110,79,84,90,66,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,76", "endOffsets": "274,355,456,590,673,738,832,905,966,1091,1159,1220,1292,1352,1406,1526,1586,1648,1702,1779,1909,1996,2078,2189,2269,2354,2445,2512,2578,2652,2733,2817,2890,2967,3044,3118,3211,3286,3376,3467,3539,3617,3708,3762,3830,3914,4001,4063,4127,4190,4300,4413,4516,4628,4705"}, "to": {"startLines": "2,34,42,43,44,46,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3213,4025,4126,4260,4412,4575,4669,4742,4803,4928,4996,5057,5129,5189,5243,5363,5423,5485,5539,5616,5746,5833,5915,6026,6106,6191,6282,6349,6415,6489,6570,6654,6727,6804,6881,6955,7048,7123,7213,7304,7376,7454,7545,7599,7667,7751,7838,7900,7964,8027,8137,8250,8353,8782", "endLines": "6,34,42,43,44,46,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,99", "endColumns": "12,80,100,133,82,64,93,72,60,124,67,60,71,59,53,119,59,61,53,76,129,86,81,110,79,84,90,66,65,73,80,83,72,76,76,73,92,74,89,90,71,77,90,53,67,83,86,61,63,62,109,112,102,111,76", "endOffsets": "324,3289,4121,4255,4338,4472,4664,4737,4798,4923,4991,5052,5124,5184,5238,5358,5418,5480,5534,5611,5741,5828,5910,6021,6101,6186,6277,6344,6410,6484,6565,6649,6722,6799,6876,6950,7043,7118,7208,7299,7371,7449,7540,7594,7662,7746,7833,7895,7959,8022,8132,8245,8348,8460,8854"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7aaeb8978a33c50dcbceb6613d676e40\\transformed\\preference-1.2.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,272,352,498,667,752", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "169,267,347,493,662,747,829"}, "to": {"startLines": "45,47,98,100,103,104,105", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4343,4477,8702,8859,9189,9358,9443", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "4407,4570,8777,9000,9353,9438,9520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\727e0e520560e457a55a364584866f29\\transformed\\navigation-ui-2.7.7\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,122", "endOffsets": "164,287"}, "to": {"startLines": "96,97", "startColumns": "4,4", "startOffsets": "8465,8579", "endColumns": "113,122", "endOffsets": "8574,8697"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc75f59f79e9064a41f591ed3352b14a\\transformed\\appcompat-1.7.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,918,1028,1136,1242,1351,1462,1565,1677,1784,1889,1989,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "220,330,439,525,629,751,833,913,1023,1131,1237,1346,1457,1560,1672,1779,1884,1984,2069,2178,2289,2388,2499,2606,2711,2885,2984,3067"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,449,559,668,754,858,980,1062,1142,1252,1360,1466,1575,1686,1789,1901,2008,2113,2213,2298,2407,2518,2617,2728,2835,2940,3114,9005", "endColumns": "119,109,108,85,103,121,81,79,109,107,105,108,110,102,111,106,104,99,84,108,110,98,110,106,104,173,98,82", "endOffsets": "444,554,663,749,853,975,1057,1137,1247,1355,1461,1570,1681,1784,1896,2003,2108,2208,2293,2402,2513,2612,2723,2830,2935,3109,3208,9083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\386f0edc44f6b3acaac787bafbf75b36\\transformed\\core-1.13.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "35,36,37,38,39,40,41,102", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3294,3392,3494,3594,3695,3802,3910,9088", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3387,3489,3589,3690,3797,3905,4020,9184"}}]}]}