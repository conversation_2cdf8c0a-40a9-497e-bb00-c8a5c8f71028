pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
        maven { url 'https://jitpack.io' }
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

rootProject.name = "android-tool"
include ':app'
include ':autojs6'  // 启用AutoJs6模块
//include ':remote-droid-guard'

// 配置AutoJs6项目路径
project(':autojs6').projectDir = new File('AutoJs6/app')

