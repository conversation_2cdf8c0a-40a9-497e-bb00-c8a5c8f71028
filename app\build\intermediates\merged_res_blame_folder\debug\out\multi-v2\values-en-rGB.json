{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\386f0edc44f6b3acaac787bafbf75b36\\transformed\\core-1.13.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3011,3107,3209,3308,3407,3511,3614,8544", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3102,3204,3303,3402,3506,3609,3725,8640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7aaeb8978a33c50dcbceb6613d676e40\\transformed\\preference-1.2.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,173,260,334,468,637,717", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "168,255,329,463,632,712,788"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4022,4155,8175,8327,8645,8814,8894", "endColumns": "67,86,73,133,168,79,75", "endOffsets": "4085,4237,8244,8456,8809,8889,8965"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\727e0e520560e457a55a364584866f29\\transformed\\navigation-ui-2.7.7\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,161", "endColumns": "105,116", "endOffsets": "156,273"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "7952,8058", "endColumns": "105,116", "endOffsets": "8053,8170"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\34b7d14a8e52fea8318500861032b192\\transformed\\material-1.5.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,402,517,596,661,751,818,877,967,1031,1094,1163,1227,1281,1393,1451,1513,1567,1639,1761,1848,1929,2039,2116,2197,2288,2355,2421,2491,2568,2655,2726,2803,2872,2941,3032,3104,3193,3282,3356,3428,3514,3564,3630,3710,3794,3856,3920,3983,4083,4180,4272,4371", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "222,299,397,512,591,656,746,813,872,962,1026,1089,1158,1222,1276,1388,1446,1508,1562,1634,1756,1843,1924,2034,2111,2192,2283,2350,2416,2486,2563,2650,2721,2798,2867,2936,3027,3099,3188,3277,3351,3423,3509,3559,3625,3705,3789,3851,3915,3978,4078,4175,4267,4366,4444"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2934,3730,3828,3943,4090,4242,4332,4399,4458,4548,4612,4675,4744,4808,4862,4974,5032,5094,5148,5220,5342,5429,5510,5620,5697,5778,5869,5936,6002,6072,6149,6236,6307,6384,6453,6522,6613,6685,6774,6863,6937,7009,7095,7145,7211,7291,7375,7437,7501,7564,7664,7761,7853,8249", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "272,3006,3823,3938,4017,4150,4327,4394,4453,4543,4607,4670,4739,4803,4857,4969,5027,5089,5143,5215,5337,5424,5505,5615,5692,5773,5864,5931,5997,6067,6144,6231,6302,6379,6448,6517,6608,6680,6769,6858,6932,7004,7090,7140,7206,7286,7370,7432,7496,7559,7659,7756,7848,7947,8322"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc75f59f79e9064a41f591ed3352b14a\\transformed\\appcompat-1.7.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,381,481,589,673,773,888,966,1041,1132,1225,1320,1414,1514,1607,1702,1796,1887,1978,2060,2163,2266,2365,2470,2574,2678,2834,8461", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "376,476,584,668,768,883,961,1036,1127,1220,1315,1409,1509,1602,1697,1791,1882,1973,2055,2158,2261,2360,2465,2569,2673,2829,2929,8539"}}]}]}