{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\34b7d14a8e52fea8318500861032b192\\transformed\\material-1.5.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,224,302,404,519,602,666,755,822,882,976,1039,1095,1165,1232,1287,1406,1463,1527,1581,1654,1776,1859,1944,2046,2124,2204,2290,2357,2423,2493,2566,2648,2720,2797,2869,2939,3032,3105,3195,3288,3362,3434,3525,3579,3645,3729,3814,3876,3940,4003,4108,4208,4303,4403", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,101,114,82,63,88,66,59,93,62,55,69,66,54,118,56,63,53,72,121,82,84,101,77,79,85,66,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,79", "endOffsets": "219,297,399,514,597,661,750,817,877,971,1034,1090,1160,1227,1282,1401,1458,1522,1576,1649,1771,1854,1939,2041,2119,2199,2285,2352,2418,2488,2561,2643,2715,2792,2864,2934,3027,3100,3190,3283,3357,3429,3520,3574,3640,3724,3809,3871,3935,3998,4103,4203,4298,4398,4478"}, "to": {"startLines": "2,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2990,3800,3902,4017,4176,4323,4412,4479,4539,4633,4696,4752,4822,4889,4944,5063,5120,5184,5238,5311,5433,5516,5601,5703,5781,5861,5947,6014,6080,6150,6223,6305,6377,6454,6526,6596,6689,6762,6852,6945,7019,7091,7182,7236,7302,7386,7471,7533,7597,7660,7765,7865,7960,8363", "endLines": "5,33,41,42,43,45,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98", "endColumns": "12,77,101,114,82,63,88,66,59,93,62,55,69,66,54,118,56,63,53,72,121,82,84,101,77,79,85,66,65,69,72,81,71,76,71,69,92,72,89,92,73,71,90,53,65,83,84,61,63,62,104,99,94,99,79", "endOffsets": "269,3063,3897,4012,4095,4235,4407,4474,4534,4628,4691,4747,4817,4884,4939,5058,5115,5179,5233,5306,5428,5511,5596,5698,5776,5856,5942,6009,6075,6145,6218,6300,6372,6449,6521,6591,6684,6757,6847,6940,7014,7086,7177,7231,7297,7381,7466,7528,7592,7655,7760,7860,7955,8055,8438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7aaeb8978a33c50dcbceb6613d676e40\\transformed\\preference-1.2.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,264,339,482,651,743", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "176,259,334,477,646,738,825"}, "to": {"startLines": "44,46,97,99,102,103,104", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4100,4240,8288,8443,8769,8938,9030", "endColumns": "75,82,74,142,168,91,86", "endOffsets": "4171,4318,8358,8581,8933,9025,9112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc75f59f79e9064a41f591ed3352b14a\\transformed\\appcompat-1.7.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "274,390,497,604,687,792,908,998,1084,1175,1268,1362,1456,1556,1649,1744,1838,1929,2020,2104,2213,2317,2415,2525,2625,2732,2891,8586", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "385,492,599,682,787,903,993,1079,1170,1263,1357,1451,1551,1644,1739,1833,1924,2015,2099,2208,2312,2410,2520,2620,2727,2886,2985,8663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\727e0e520560e457a55a364584866f29\\transformed\\navigation-ui-2.7.7\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,116", "endOffsets": "161,278"}, "to": {"startLines": "95,96", "startColumns": "4,4", "startOffsets": "8060,8171", "endColumns": "110,116", "endOffsets": "8166,8283"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\386f0edc44f6b3acaac787bafbf75b36\\transformed\\core-1.13.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "34,35,36,37,38,39,40,101", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3068,3170,3273,3378,3483,3582,3686,8668", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "3165,3268,3373,3478,3577,3681,3795,8764"}}]}]}