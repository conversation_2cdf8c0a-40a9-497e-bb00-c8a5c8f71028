{"logs": [{"outputFile": "com.bm.atool.app-mergeDebugResources-43:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\fc75f59f79e9064a41f591ed3352b14a\\transformed\\appcompat-1.7.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,34,36,38,39,40,41,43,45,46,47,48,49,51,53,55,57,59,61,62,67,69,71,72,73,75,77,78,79,80,85,96,139,142,185,200,209,211,213,215,218,222,225,226,227,230,231,232,233,234,235,238,239,241,243,245,247,251,253,254,255,256,258,262,264,266,267,268,269,270,271,299,300,301,311,312,313,325", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1668,1759,1862,1965,2070,2177,2286,2395,2504,2613,2722,2829,2932,3051,3206,3361,3466,3587,3688,3835,3976,4079,4198,4305,4408,4563,4734,4883,5048,5205,5356,5475,5826,5975,6124,6236,6383,6536,6683,6758,6847,6934,7459,8430,11188,11373,14143,15276,15919,16042,16165,16278,16461,16716,16917,17006,17117,17350,17451,17546,17669,17798,17915,18092,18191,18326,18469,18604,18723,18924,19043,19136,19247,19303,19410,19605,19716,19849,19944,20035,20126,20219,20336,22568,22639,22722,23345,23402,23460,24084", "endLines": "21,22,23,24,25,26,27,28,29,30,31,32,33,35,37,38,39,40,42,44,45,46,47,48,50,52,54,56,58,60,61,66,68,70,71,72,74,76,77,78,79,80,85,138,141,184,187,202,210,212,214,217,221,224,225,226,229,230,231,232,233,234,237,238,240,242,244,246,250,252,253,254,255,257,261,263,265,266,267,268,269,270,272,299,300,310,311,312,324,336", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1754,1857,1960,2065,2172,2281,2390,2499,2608,2717,2824,2927,3046,3201,3356,3461,3582,3683,3830,3971,4074,4193,4300,4403,4558,4729,4878,5043,5200,5351,5470,5821,5970,6119,6231,6378,6531,6678,6753,6842,6929,7030,7557,11183,11368,14138,14335,15470,16037,16160,16273,16456,16711,16912,17001,17112,17345,17446,17541,17664,17793,17910,18087,18186,18321,18464,18599,18718,18919,19038,19131,19242,19298,19405,19600,19711,19844,19939,20030,20121,20214,20331,20470,22634,22717,23340,23397,23455,24079,24715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\34b7d14a8e52fea8318500861032b192\\transformed\\material-1.5.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,49,52,57,68,74,78,82,86,90,94,100,106,112,118,122,126,127,128,129,133,136,139,142,145,148,151,155,159", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,2052,2311,2582,2800,3032,3268,3518,3731,3962,4078,4248,4569,5598,6055,6275,6497,6717,6939,7159,7501,7845,8195,8545,8874,9215,9353,9497,9653,10046,10264,10486,10712,10928,11098,11288,11529,11788", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,23,26,29,32,35,38,41,44,47,48,51,56,67,73,77,81,85,89,93,99,105,111,117,121,125,126,127,128,132,135,138,141,144,147,150,154,158,161", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,2047,2306,2577,2795,3027,3263,3513,3726,3957,4073,4243,4564,5593,6050,6270,6492,6712,6934,7154,7496,7840,8190,8540,8869,9210,9348,9492,9648,10041,10259,10481,10707,10923,11093,11283,11524,11783,11960"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,81,82,83,84,86,87,90,93,188,191,194,197,203,206,273,274,277,282,293,347,351,355,359,363,367,373,379,385,391,395,399,400,401,402,406,409,412,415,426,429,432,436,440", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,7035,7128,7235,7340,7562,7687,7900,8159,14340,14558,14790,15026,15475,15688,20475,20591,20761,21082,22111,25550,25720,25892,26062,26234,26404,26696,26990,27290,27590,27919,28260,28398,28542,28698,29091,29309,29531,29757,30497,30667,30857,31098,31357", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,81,82,83,84,86,89,92,95,190,193,196,199,205,208,273,276,281,292,298,350,354,358,362,366,372,378,384,390,394,398,399,400,401,405,408,411,414,417,428,431,435,439,442", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,7123,7230,7335,7454,7682,7895,8154,8425,14553,14785,15021,15271,15683,15914,20586,20756,21077,22106,22563,25715,25887,26057,26229,26399,26691,26985,27285,27585,27914,28255,28393,28537,28693,29086,29304,29526,29752,29968,30662,30852,31093,31352,31529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\7aaeb8978a33c50dcbceb6613d676e40\\transformed\\preference-1.2.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,6", "startColumns": "4,4,4", "startOffsets": "55,120,276", "endLines": "2,5,8", "endColumns": "64,12,12", "endOffsets": "115,271,449"}, "to": {"startLines": "20,337,340", "startColumns": "4,4,4", "startOffsets": "1603,24720,24876", "endLines": "20,339,342", "endColumns": "64,12,12", "endOffsets": "1663,24871,25049"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\386f0edc44f6b3acaac787bafbf75b36\\transformed\\core-1.13.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,343,344,345,346,418,421", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,25054,25170,25296,25422,29973,30145", "endLines": "2,17,18,19,343,344,345,346,420,425", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,25165,25291,25417,25545,30140,30492"}}]}]}