# 微信朋友圈测试脚本说明

## 功能概述

新增了一个微信朋友圈测试脚本按键，实现自动启动微信、切换到发现界面、并点击进入朋友圈的完整流程。

## 脚本功能

### 1. 自动启动微信
- 检测微信app是否已安装 (`com.tencent.mm`)
- 自动启动微信应用
- 带重试机制，确保启动成功

### 2. 智能导航到发现页面
- **方法1**: 通过文本识别点击"发现"标签
- **方法2**: 如果文本识别失败，使用坐标点击底部导航栏发现位置
- 自适应不同屏幕尺寸

### 3. 进入朋友圈
- **方法1**: 通过文本识别点击"朋友圈"
- **方法2**: 如果文本识别失败，使用坐标点击朋友圈入口位置
- 智能等待页面加载

### 4. 朋友圈浏览验证
- 验证是否成功进入朋友圈
- 模拟真实用户浏览行为（3次向下滑动）
- 提供详细的执行状态反馈

## 技术特点

### 双重点击策略
```javascript
// 方法1: 文本识别点击
var discoverClicked = clickText('发现', '发现标签');

// 方法2: 坐标点击作为备选
if (!discoverClicked) {
    var discoverX = screenWidth * 0.625; // 发现标签位置
    var discoverY = screenHeight * 0.95;  // 底部导航栏
    safeClick(discoverX, discoverY, '发现标签(坐标)');
}
```

### 人性化操作模拟
- **随机延迟**: 所有操作都有随机延迟时间
- **安全点击**: 带描述和日志的点击函数
- **智能等待**: 根据页面加载情况调整等待时间

### 屏幕适配
- 基于屏幕比例计算点击位置
- 支持不同分辨率的Android设备
- 自动适应不同的屏幕尺寸

## 执行流程

1. **启动阶段**
   - 显示开始提示
   - 检查微信是否安装
   - 启动微信应用

2. **导航阶段**
   - 点击底部"发现"标签
   - 等待发现页面加载
   - 点击"朋友圈"入口

3. **验证阶段**
   - 检查是否成功进入朋友圈
   - 进行简单的浏览操作
   - 显示执行结果

## UI界面更新

### 新增按钮
- **按钮ID**: `btnExecuteWechatScript`
- **按钮文本**: "执行微信朋友圈测试脚本 (启动微信→发现→朋友圈)"
- **按钮颜色**: 绿色背景 (#4CAF50)
- **位置**: 在今日头条脚本按钮下方

### 代码文件修改

#### 1. fragment_simple_autojs.xml
```xml
<Button
    android:id="@+id/btnExecuteWechatScript"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:text="执行微信朋友圈测试脚本 (启动微信→发现→朋友圈)"
    android:textColor="@android:color/white"
    android:background="#4CAF50"
    android:layout_marginBottom="8dp"
    android:padding="16dp"
    android:textSize="16sp"
    android:elevation="2dp" />
```

#### 2. SimpleAutoJsFragment.java
- 添加按钮声明: `btnExecuteWechatScript`
- 添加按钮初始化和事件绑定
- 实现 `executeWechatScript()` 方法

## 错误处理

### 应用检测
- 检查微信是否已安装
- 启动失败时的重试机制
- 详细的错误日志输出

### 导航失败处理
- 文本识别失败时的坐标点击备选方案
- 页面加载超时处理
- 当前应用状态验证

### 用户反馈
- 每个步骤都有Toast提示
- 详细的控制台日志
- 成功/失败状态的明确反馈

## 使用方法

1. 确保设备已安装微信应用
2. 启用无障碍服务权限
3. 点击"执行微信朋友圈测试脚本"按钮
4. 观察脚本自动执行导航流程
5. 查看执行结果和状态反馈

## 注意事项

- 需要无障碍服务权限才能正常工作
- 首次使用可能需要手动授权相关权限
- 不同版本的微信界面可能略有差异
- 建议在网络良好的环境下使用，确保页面加载正常

这个测试脚本可以帮助验证自动化操作的准确性，也可以作为其他微信相关自动化功能的基础模板。
