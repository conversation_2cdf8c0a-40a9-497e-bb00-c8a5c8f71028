-- Merging decision tree log ---
manifest
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-225:12
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-225:12
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-225:12
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:1-225:12
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1f279812170284460bbcdc39eaaa88b4\transformed\viewbinding-8.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3d6207db6a505f9bc28da3cd9f6ed623\transformed\navigation-common-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a6383c6b1eba85ad21a8f422628f8f32\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a58c99ead715c15ce041b15e487251b3\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\727e0e520560e457a55a364584866f29\transformed\navigation-ui-2.7.7\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\34b7d14a8e52fea8318500861032b192\transformed\material-1.5.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\5eec29715fc25863a3261b807f74fe19\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7aaeb8978a33c50dcbceb6613d676e40\transformed\preference-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\b69a42f7b19ba2085e061021cdc926d1\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc75f59f79e9064a41f591ed3352b14a\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\29210904bc2be59aef459e4dd3849e6d\transformed\safe-parcel-1.7.0\AndroidManifest.xml:6:1-14:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\243d8e655dd2c79617a3c5902410736b\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8ac2e8262d6d0ea9f6aa7acf72ee5ca0\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b3ed9753e71f1b21d517dff3bcbf3692\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\c623598b4722168dfd91adb73bee9322\transformed\activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\f32cdae79064b8e14ebc5856327a004f\transformed\activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\bf445c5278d90d695ee427357d4e0d9a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\60424e5940ee863245601e989f7d3d47\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1631dd0a121e9ca09550994addde8c29\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a9fe6f1f3d4f1955831bd0bd8505cf9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8708c4b0ce446ce64cb73c2285ff2d4\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45c925985a73f43532642179c4a5bca8\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8a0236c4dae925f66eff3ca8aa269623\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fb1aa120a5e856b1ee14ecbd5ed2bf65\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\237c220db949eef0681410fad529793f\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\51d83df32570a04eab17933b3d816584\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ddf917ae19002c8db9d1dd97a875baa3\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\25eceab4b6241f255f8a90b1322e498d\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d0341872783df9cce8b8906f9aff5a3c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac2dd5ffaa98f85d42ad49a82d666f3e\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\990237958d5d562996c2c6c12758b360\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b027b8f2e6ab461f020debc259262a93\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2b0cb8f4445c82eaf52fc499e54cc37\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\62c5e53b3f5be3f017685fc176349494\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\78a3828ce1220683bf0bb8eb214f50f8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\18eb1296477219ab08c614fd1ff6faff\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\78e785894b0ae4a3c5b8a7b3a3f90361\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\48525cbeb87d2eeb31d05df28e62d4fb\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d3773cfdc2ff7e7c8b24f6001ffa609\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\12d1dbd7617fe486b3c0f647c0f6a584\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8c385dc33536a39046a95f24db0eae61\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\3583d8d2aa2457ff202ceae4a568fee0\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\4677cc4f5db07b8c47bb9287d2839099\transformed\FreeReflection-3.2.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe7a7604bc4c954e53744e65cf225451\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ca45c2bf79059da5f5a0b4cfb06d06d5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e17337a6855111d55020e1e767499d23\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5675ae5853b182b6f60ac633e9d44c89\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\861ded2ab871bff1797b1ae8c0c5986a\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b3676f5d1bb8307ef660f3eca94be68\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b99468cfd3d5831def067b461a5b1324\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\17fa5c1c42d15d936a523cb8173e28e0\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c41738211ab47e3f2c2fb3cfce1ec34\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6565c90255b4e71461a8f7da3d9dfe93\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5bc40c5b8cd39d8b7e83407100aa3867\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:4:5-27
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.hardware.telephony
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:6:5-8:36
	android:required
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:8:9-33
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-50
uses-permission#android.permission.READ_PRIVILEGED_PHONE_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:10:5-11:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:11:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:10:22-83
uses-permission#android.permission.RECEIVE_SMS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-70
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-67
uses-permission#android.permission.READ_SMS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
uses-permission#android.permission.SEND_SMS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-67
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-64
uses-permission#android.permission.WRITE_SMS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-68
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-65
uses-permission#android.permission.INTERNET
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.READ_PHONE_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-75
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-72
uses-permission#android.permission.READ_PHONE_NUMBERS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-76
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-74
uses-permission#android.permission.WAKE_LOCK
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-68
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:22-65
permission#android.permission.DEVICE_POWER
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:21:5-22:51
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:22:9-48
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:21:17-63
uses-permission#android.permission.BIND_JOB_SERVICE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:23:5-24:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:24:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:23:22-72
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:27:5-76
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:27:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-80
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-77
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
uses-permission#android.permission.WRITE_MEDIA_IMAGES
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-74
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:33:5-81
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:33:22-78
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-92
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-89
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-95
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-92
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-75
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:38:5-39:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:39:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:38:22-82
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:40:5-80
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:40:22-78
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:5-81
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:44:5-45:40
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:45:9-37
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:44:22-79
uses-permission#android.permission.VIBRATE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:5-66
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:22-63
uses-permission#android.permission.CAMERA
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:5-65
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:5-71
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:22-68
uses-permission#android.permission.CAPTURE_VIDEO_OUTPUT
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:49:5-50:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:50:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:49:22-76
uses-permission#android.permission.CAPTURE_SECURE_VIDEO_OUTPUT
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:51:5-52:47
	tools:ignore
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:52:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:51:22-83
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:5-94
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:22-91
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:54:5-89
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:54:22-86
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:55:5-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:55:22-74
application
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:56:5-223:19
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:56:5-223:19
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\34b7d14a8e52fea8318500861032b192\transformed\material-1.5.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\34b7d14a8e52fea8318500861032b192\transformed\material-1.5.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\5eec29715fc25863a3261b807f74fe19\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\5eec29715fc25863a3261b807f74fe19\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5675ae5853b182b6f60ac633e9d44c89\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5675ae5853b182b6f60ac633e9d44c89\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b99468cfd3d5831def067b461a5b1324\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b99468cfd3d5831def067b461a5b1324\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:63:9-35
	android:label
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:61:9-41
	android:fullBackupContent
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:59:9-54
	android:roundIcon
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:62:9-54
	tools:targetApi
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:67:9-29
	android:icon
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:60:9-43
	android:allowBackup
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:57:9-35
	android:theme
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:64:9-49
	android:dataExtractionRules
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:58:9-65
	android:usesCleartextTraffic
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:65:9-44
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:66:9-28
activity#com.bm.atool.LoginActivity
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:68:9-70:40
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:70:13-37
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:69:13-42
activity#com.bm.atool.MainActivity
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:71:9-84:20
	android:label
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:74:13-45
	android:excludeFromRecents
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:78:13-46
	android:launchMode
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-44
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:73:13-36
	android:theme
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-53
	android:taskAffinity
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:79:13-83:29
action#android.intent.action.MAIN
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:80:17-69
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:80:25-66
category#android.intent.category.LAUNCHER
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:82:17-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:82:27-74
activity#com.bm.atool.service.singlepixel.SinglePixelActivity
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:85:9-87:39
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:87:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:86:13-68
receiver#com.bm.atool.receivers.SimChangedReceiver
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:89:9-95:20
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-57
intent-filter#action:name:android.intent.action.SIM_STATE_CHANGED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:92:13-94:29
action#android.intent.action.SIM_STATE_CHANGED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:93:17-81
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:93:25-79
receiver#com.bm.atool.receivers.SmsReceiver
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:9-102:20
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:97:13-36
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:98:13-66
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:19-56
intent-filter#action:name:android.provider.Telephony.SMS_RECEIVED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:99:13-101:29
action#android.provider.Telephony.SMS_RECEIVED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:100:17-81
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:100:25-79
receiver#com.bm.atool.receivers.WakeUpReceiver
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:104:9-117:20
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-37
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:107:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-53
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED+action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MEDIA_MOUNTED+action:name:android.intent.action.USER_PRESENT+action:name:android.intent.action.USER_PRESENT+action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:13-116:29
action#android.intent.action.USER_PRESENT
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-76
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-74
action#android.intent.action.BOOT_COMPLETED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:111:17-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:111:25-76
action#android.intent.action.MEDIA_MOUNTED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:17-78
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:25-75
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-87
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:115:17-90
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:115:25-87
receiver#com.bm.atool.receivers.WakeUpAutoStartReceiver
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:118:9-145:20
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:120:13-37
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:121:13-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-62
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:123:13-126:29
intent-filter#action:name:android.intent.action.PACKAGE_ADDED+action:name:android.intent.action.PACKAGE_REMOVED+data:scheme:package
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:128:13-132:29
action#android.intent.action.PACKAGE_ADDED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:129:17-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:129:25-75
action#android.intent.action.PACKAGE_REMOVED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:25-77
data
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:131:17-49
	android:scheme
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:131:23-47
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE+action:name:android.net.wifi.STATE_CHANGE+action:name:android.net.wifi.WIFI_STATE_CJANGED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:134:13-138:29
action#android.net.wifi.WIFI_STATE_CJANGED
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:17-77
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:25-75
action#android.net.wifi.STATE_CHANGE
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:137:17-71
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:137:25-69
intent-filter#action:name:android.intent.action.MEDIA_EJECT+action:name:android.intent.action.MEDIA_MOUNTED+data:scheme:file
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:140:13-144:29
action#android.intent.action.MEDIA_EJECT
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:141:17-75
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:141:25-73
service#com.bm.atool.service.JobSchedulerService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:148:9-153:43
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:153:13-41
	android:enabled
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:151:13-35
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:152:13-36
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:150:13-69
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-56
service#com.bm.atool.service.WatchDogService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:155:9-160:43
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:160:13-41
	android:enabled
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:158:13-35
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:159:13-36
	android:foregroundServiceType
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:157:13-58
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:156:13-52
service#com.bm.atool.service.PlayMusicService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:162:9-164:46
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:164:13-44
	android:foregroundServiceType
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:163:13-58
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:162:18-58
service#com.bm.atool.service.ANTAccessibilityService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:165:9-178:19
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:169:13-45
	android:enabled
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:167:13-35
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:168:13-36
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:170:13-79
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:166:13-60
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:171:13-173:29
action#android.accessibilityservice.AccessibilityService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:172:17-92
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:172:25-89
meta-data#android.accessibilityservice
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:175:13-177:54
	android:resource
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:177:17-51
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:176:17-60
service#com.bm.atool.service.SocketService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:179:9-195:19
	android:process
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:183:13-38
	android:label
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:182:13-42
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:181:13-37
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:184:13-69
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:180:13-50
intent-filter#action:name:android.net.VpnService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:185:13-187:29
action#android.net.VpnService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:186:17-65
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:186:25-62
meta-data#android.net.VpnService.SUPPORTS_ALWAYS_ON
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:188:13-190:39
	android:value
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:190:17-37
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:189:17-73
property
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:191:13-193:38
	android:value
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:193:17-36
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:192:17-76
service#com.bm.atool.service.NotificationService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:197:9-205:19
	android:label
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:199:13-45
	android:exported
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:201:13-36
	android:permission
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:200:13-87
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:198:13-56
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:202:13-204:29
action#android.service.notification.NotificationListenerService
ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:203:17-99
	android:name
		ADDED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:203:25-96
uses-sdk
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1f279812170284460bbcdc39eaaa88b4\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1f279812170284460bbcdc39eaaa88b4\transformed\viewbinding-8.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3d6207db6a505f9bc28da3cd9f6ed623\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\3d6207db6a505f9bc28da3cd9f6ed623\transformed\navigation-common-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a6383c6b1eba85ad21a8f422628f8f32\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a6383c6b1eba85ad21a8f422628f8f32\transformed\navigation-runtime-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a58c99ead715c15ce041b15e487251b3\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-fragment:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\a58c99ead715c15ce041b15e487251b3\transformed\navigation-fragment-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\727e0e520560e457a55a364584866f29\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-ui:2.7.7] C:\Users\<USER>\.gradle\caches\transforms-4\727e0e520560e457a55a364584866f29\transformed\navigation-ui-2.7.7\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\34b7d14a8e52fea8318500861032b192\transformed\material-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\34b7d14a8e52fea8318500861032b192\transformed\material-1.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\5eec29715fc25863a3261b807f74fe19\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\5eec29715fc25863a3261b807f74fe19\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7aaeb8978a33c50dcbceb6613d676e40\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\7aaeb8978a33c50dcbceb6613d676e40\transformed\preference-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\b69a42f7b19ba2085e061021cdc926d1\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\b69a42f7b19ba2085e061021cdc926d1\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc75f59f79e9064a41f591ed3352b14a\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\fc75f59f79e9064a41f591ed3352b14a\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\29210904bc2be59aef459e4dd3849e6d\transformed\safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [org.microg:safe-parcel:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\29210904bc2be59aef459e4dd3849e6d\transformed\safe-parcel-1.7.0\AndroidManifest.xml:10:5-12:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\243d8e655dd2c79617a3c5902410736b\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\243d8e655dd2c79617a3c5902410736b\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8ac2e8262d6d0ea9f6aa7acf72ee5ca0\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8ac2e8262d6d0ea9f6aa7acf72ee5ca0\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b3ed9753e71f1b21d517dff3bcbf3692\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\b3ed9753e71f1b21d517dff3bcbf3692\transformed\fragment-ktx-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\c623598b4722168dfd91adb73bee9322\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\c623598b4722168dfd91adb73bee9322\transformed\activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\f32cdae79064b8e14ebc5856327a004f\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] C:\Users\<USER>\.gradle\caches\transforms-4\f32cdae79064b8e14ebc5856327a004f\transformed\activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\bf445c5278d90d695ee427357d4e0d9a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\bf445c5278d90d695ee427357d4e0d9a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\60424e5940ee863245601e989f7d3d47\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\60424e5940ee863245601e989f7d3d47\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1631dd0a121e9ca09550994addde8c29\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1631dd0a121e9ca09550994addde8c29\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a9fe6f1f3d4f1955831bd0bd8505cf9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4a9fe6f1f3d4f1955831bd0bd8505cf9\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8708c4b0ce446ce64cb73c2285ff2d4\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8708c4b0ce446ce64cb73c2285ff2d4\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45c925985a73f43532642179c4a5bca8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\45c925985a73f43532642179c4a5bca8\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8a0236c4dae925f66eff3ca8aa269623\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8a0236c4dae925f66eff3ca8aa269623\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fb1aa120a5e856b1ee14ecbd5ed2bf65\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\fb1aa120a5e856b1ee14ecbd5ed2bf65\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\237c220db949eef0681410fad529793f\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\237c220db949eef0681410fad529793f\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\51d83df32570a04eab17933b3d816584\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\51d83df32570a04eab17933b3d816584\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ddf917ae19002c8db9d1dd97a875baa3\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\ddf917ae19002c8db9d1dd97a875baa3\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\25eceab4b6241f255f8a90b1322e498d\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\25eceab4b6241f255f8a90b1322e498d\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d0341872783df9cce8b8906f9aff5a3c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\d0341872783df9cce8b8906f9aff5a3c\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac2dd5ffaa98f85d42ad49a82d666f3e\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac2dd5ffaa98f85d42ad49a82d666f3e\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\990237958d5d562996c2c6c12758b360\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\990237958d5d562996c2c6c12758b360\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b027b8f2e6ab461f020debc259262a93\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\b027b8f2e6ab461f020debc259262a93\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2b0cb8f4445c82eaf52fc499e54cc37\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a2b0cb8f4445c82eaf52fc499e54cc37\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\62c5e53b3f5be3f017685fc176349494\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\62c5e53b3f5be3f017685fc176349494\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\78a3828ce1220683bf0bb8eb214f50f8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\78a3828ce1220683bf0bb8eb214f50f8\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\18eb1296477219ab08c614fd1ff6faff\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\18eb1296477219ab08c614fd1ff6faff\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\78e785894b0ae4a3c5b8a7b3a3f90361\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\78e785894b0ae4a3c5b8a7b3a3f90361\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\48525cbeb87d2eeb31d05df28e62d4fb\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\48525cbeb87d2eeb31d05df28e62d4fb\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d3773cfdc2ff7e7c8b24f6001ffa609\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d3773cfdc2ff7e7c8b24f6001ffa609\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\12d1dbd7617fe486b3c0f647c0f6a584\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\12d1dbd7617fe486b3c0f647c0f6a584\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8c385dc33536a39046a95f24db0eae61\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8c385dc33536a39046a95f24db0eae61\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\3583d8d2aa2457ff202ceae4a568fee0\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\3583d8d2aa2457ff202ceae4a568fee0\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\4677cc4f5db07b8c47bb9287d2839099\transformed\FreeReflection-3.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.tiann:FreeReflection:3.2.2] C:\Users\<USER>\.gradle\caches\transforms-4\4677cc4f5db07b8c47bb9287d2839099\transformed\FreeReflection-3.2.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe7a7604bc4c954e53744e65cf225451\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe7a7604bc4c954e53744e65cf225451\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ca45c2bf79059da5f5a0b4cfb06d06d5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ca45c2bf79059da5f5a0b4cfb06d06d5\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e17337a6855111d55020e1e767499d23\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e17337a6855111d55020e1e767499d23\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5675ae5853b182b6f60ac633e9d44c89\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5675ae5853b182b6f60ac633e9d44c89\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\861ded2ab871bff1797b1ae8c0c5986a\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\861ded2ab871bff1797b1ae8c0c5986a\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b3676f5d1bb8307ef660f3eca94be68\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4b3676f5d1bb8307ef660f3eca94be68\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b99468cfd3d5831def067b461a5b1324\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\b99468cfd3d5831def067b461a5b1324\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\17fa5c1c42d15d936a523cb8173e28e0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\17fa5c1c42d15d936a523cb8173e28e0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c41738211ab47e3f2c2fb3cfce1ec34\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6c41738211ab47e3f2c2fb3cfce1ec34\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6565c90255b4e71461a8f7da3d9dfe93\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6565c90255b4e71461a8f7da3d9dfe93\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5bc40c5b8cd39d8b7e83407100aa3867\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5bc40c5b8cd39d8b7e83407100aa3867\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5675ae5853b182b6f60ac633e9d44c89\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\5675ae5853b182b6f60ac633e9d44c89\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
