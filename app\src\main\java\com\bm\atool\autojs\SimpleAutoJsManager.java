package com.bm.atool.autojs;

import android.app.Application;
import android.content.Context;
import android.util.Log;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 简化的AutoJs管理器
 * 提供基本的JavaScript脚本执行功能
 */
public class SimpleAutoJsManager {
    private static final String TAG = "SimpleAutoJsManager";
    
    private static SimpleAutoJsManager instance;
    private Context context;
    private boolean isInitialized = false;
    private ConcurrentHashMap<String, SimpleJsEngine> runningScripts;
    private AtomicInteger scriptCounter;
    
    private SimpleAutoJsManager() {
        runningScripts = new ConcurrentHashMap<>();
        scriptCounter = new AtomicInteger(0);
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized SimpleAutoJsManager getInstance() {
        if (instance == null) {
            instance = new SimpleAutoJsManager();
        }
        return instance;
    }
    
    /**
     * 初始化管理器
     */
    public boolean initialize(Application application) {
        if (isInitialized) {
            Log.d(TAG, "SimpleAutoJsManager already initialized");
            return true;
        }
        
        try {
            this.context = application.getApplicationContext();
            isInitialized = true;
            Log.d(TAG, "SimpleAutoJsManager initialized successfully");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize SimpleAutoJsManager", e);
            return false;
        }
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 执行JavaScript脚本
     */
    public String executeScript(String scriptContent, String scriptName, ScriptExecutionCallback callback) {
        if (!isInitialized) {
            Log.e(TAG, "SimpleAutoJsManager not initialized");
            return null;
        }
        
        try {
            // 生成唯一的脚本ID
            String scriptId = scriptName + "_" + scriptCounter.incrementAndGet();
            Log.d(TAG, "Creating script execution: " + scriptName + " (ID: " + scriptId + ")");
            
            // 创建JS引擎
            SimpleJsEngine engine = new SimpleJsEngine(context);
            runningScripts.put(scriptId, engine);
            Log.d(TAG, "JS Engine created for script: " + scriptName);
            
            // 执行脚本
            engine.executeScript(scriptContent, scriptName, new SimpleJsEngine.JsExecutionCallback() {
                @Override
                public void onStart(String scriptName) {
                    Log.d(TAG, "Script started: " + scriptName + " (ID: " + scriptId + ")");
                    if (callback != null) {
                        callback.onStart(scriptName);
                    }
                }
                
                @Override
                public void onSuccess(String scriptName, String result) {
                    Log.d(TAG, "Script completed: " + scriptName + ", result: " + result + " (ID: " + scriptId + ")");
                    runningScripts.remove(scriptId);
                    if (callback != null) {
                        callback.onSuccess(scriptName, result);
                    }
                }
                
                @Override
                public void onError(String scriptName, String error) {
                    Log.e(TAG, "Script failed: " + scriptName + " (ID: " + scriptId + ")");
                    // 分行记录错误信息
                    String[] errorLines = error.split("\n");
                    for (int i = 0; i < errorLines.length; i++) {
                        Log.e(TAG, "Error detail [" + i + "]: " + errorLines[i]);
                    }
                    runningScripts.remove(scriptId);
                    if (callback != null) {
                        callback.onError(scriptName, error);
                    }
                }
            });
            
            Log.d(TAG, "Script execution initiated: " + scriptName + " (ID: " + scriptId + ")");
            return scriptId;
            
        } catch (Exception e) {
            Log.e(TAG, "Error executing script: " + scriptName, e);
            return null;
        }
    }
    
    /**
     * 停止指定脚本
     */
    public void stopScript(String scriptId) {
        try {
            SimpleJsEngine engine = runningScripts.get(scriptId);
            if (engine != null) {
                engine.stopScript();
                runningScripts.remove(scriptId);
                Log.d(TAG, "Script stopped: " + scriptId);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error stopping script: " + scriptId, e);
        }
    }
    
    /**
     * 停止所有脚本
     */
    public void stopAllScripts() {
        try {
            for (String scriptId : runningScripts.keySet()) {
                SimpleJsEngine engine = runningScripts.get(scriptId);
                if (engine != null) {
                    engine.stopScript();
                }
            }
            runningScripts.clear();
            Log.d(TAG, "All scripts stopped");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping all scripts", e);
        }
    }
    
    /**
     * 获取运行中的脚本数量
     */
    public int getRunningScriptCount() {
        return runningScripts.size();
    }
    
    /**
     * 检查无障碍服务是否启用（简化版本）
     */
    public boolean isAccessibilityServiceEnabled() {
        // 简化版本，总是返回false，因为我们没有实际的无障碍服务
        return false;
    }
    
    /**
     * 释放资源
     */
    public void release() {
        if (isInitialized) {
            try {
                stopAllScripts();
                Log.d(TAG, "SimpleAutoJsManager released");
            } catch (Exception e) {
                Log.e(TAG, "Error during release", e);
            } finally {
                isInitialized = false;
                context = null;
            }
        }
    }
    
    /**
     * 脚本执行回调接口
     */
    public interface ScriptExecutionCallback {
        void onStart(String scriptName);
        void onSuccess(String scriptName, String result);
        void onError(String scriptName, String error);
    }
}
