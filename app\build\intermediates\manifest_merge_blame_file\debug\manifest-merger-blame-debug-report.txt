1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bm.atool"
4    android:versionCode="1"
5    android:versionName="1.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-feature
11-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:6:5-8:36
12        android:name="android.hardware.telephony"
12-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-50
13        android:required="false" />
13-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:8:9-33
14
15    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
15-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:10:5-11:47
15-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:10:22-83
16    <uses-permission android:name="android.permission.RECEIVE_SMS" />
16-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-70
16-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-67
17    <uses-permission android:name="android.permission.READ_SMS" />
17-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
17-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
18    <uses-permission android:name="android.permission.SEND_SMS" />
18-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-67
18-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-64
19    <uses-permission android:name="android.permission.WRITE_SMS" />
19-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-68
19-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-65
20    <uses-permission android:name="android.permission.INTERNET" />
20-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
20-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
21    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
21-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-75
21-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-72
22    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
22-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-76
22-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-77
23-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-74
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-68
24-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:20:22-65
25
26    <permission android:name="android.permission.DEVICE_POWER" />
26-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:21:5-22:51
26-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:21:17-63
27
28    <uses-permission android:name="android.permission.BIND_JOB_SERVICE" />
28-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:23:5-24:47
28-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:23:22-72
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
29-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
30    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
30-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-79
30-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-76
31    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
31-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:27:5-76
31-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:27:22-73
32    <uses-permission android:name="android.permission.INTERNET" />
32-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
32-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-80
33-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-77
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
34-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
34-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
35    <uses-permission android:name="android.permission.WRITE_MEDIA_IMAGES" />
35-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-77
35-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-74
36    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
36-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-79
36-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-76
37    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
37-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:33:5-81
37-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:33:22-78
38    <uses-permission android:name="android.permission.INTERNET" />
38-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
38-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
39-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-92
39-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-89
40    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
40-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-95
40-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-92
41    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
41-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-77
41-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-75
42    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
42-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:38:5-39:47
42-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
43-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:40:5-80
43-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:40:22-78
44
45    <!-- AutoJs6 additional permissions -->
46    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
46-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:5-81
46-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:43:22-78
47    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
47-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:44:5-45:40
47-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:44:22-79
48    <uses-permission android:name="android.permission.VIBRATE" />
48-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:5-66
48-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:46:22-63
49    <uses-permission android:name="android.permission.CAMERA" />
49-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:5-65
49-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:47:22-62
50    <uses-permission android:name="android.permission.RECORD_AUDIO" />
50-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:5-71
50-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:48:22-68
51    <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" />
51-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:49:5-50:47
51-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:49:22-76
52    <uses-permission android:name="android.permission.CAPTURE_SECURE_VIDEO_OUTPUT" />
52-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:51:5-52:47
52-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:51:22-83
53    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
53-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:5-94
53-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:53:22-91
54    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
54-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:54:5-89
54-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:54:22-86
55    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
55-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:55:5-77
55-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:55:22-74
56
57    <permission
57-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
58        android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
59        android:protectionLevel="signature" />
59-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
60
61    <uses-permission android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
61-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
61-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
62
63    <application
63-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:56:5-223:19
64        android:name="com.bm.atool.App"
64-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:66:9-28
65        android:allowBackup="true"
65-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:57:9-35
66        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
66-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\386f0edc44f6b3acaac787bafbf75b36\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
67        android:dataExtractionRules="@xml/data_extraction_rules"
67-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:58:9-65
68        android:debuggable="true"
69        android:extractNativeLibs="false"
70        android:fullBackupContent="@xml/backup_rules"
70-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:59:9-54
71        android:icon="@mipmap/ic_launcher"
71-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:60:9-43
72        android:label="@string/app_name"
72-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:61:9-41
73        android:roundIcon="@mipmap/ic_launcher_round"
73-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:62:9-54
74        android:supportsRtl="true"
74-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:63:9-35
75        android:testOnly="true"
76        android:theme="@style/Theme.AndroidTool"
76-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:64:9-49
77        android:usesCleartextTraffic="true" >
77-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:65:9-44
78        <activity
78-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:68:9-70:40
79            android:name="com.bm.atool.LoginActivity"
79-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:69:13-42
80            android:exported="false" />
80-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:70:13-37
81        <activity
81-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:71:9-84:20
82            android:name="com.bm.atool.MainActivity"
82-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-41
83            android:excludeFromRecents="true"
83-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:78:13-46
84            android:exported="true"
84-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:73:13-36
85            android:label="@string/app_name"
85-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:74:13-45
86            android:launchMode="singleTask"
86-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-44
87            android:taskAffinity=""
87-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-36
88            android:theme="@style/Theme.AndroidTool" >
88-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-53
89            <intent-filter>
89-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:79:13-83:29
90                <action android:name="android.intent.action.MAIN" />
90-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:80:17-69
90-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:80:25-66
91
92                <category android:name="android.intent.category.LAUNCHER" />
92-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:82:17-77
92-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:82:27-74
93            </intent-filter>
94        </activity>
95        <activity
95-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:85:9-87:39
96            android:name="com.bm.atool.service.singlepixel.SinglePixelActivity"
96-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:86:13-68
97            android:exported="true" />
97-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:87:13-36
98
99        <receiver
99-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:89:9-95:20
100            android:name="com.bm.atool.receivers.SimChangedReceiver"
100-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-57
101            android:exported="true" >
101-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-36
102            <intent-filter>
102-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:92:13-94:29
103                <action android:name="android.intent.action.SIM_STATE_CHANGED" />
103-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:93:17-81
103-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:93:25-79
104            </intent-filter>
105        </receiver>
106        <receiver
106-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:9-102:20
107            android:name="com.bm.atool.receivers.SmsReceiver"
107-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:96:19-56
108            android:exported="true"
108-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:97:13-36
109            android:permission="android.permission.BROADCAST_SMS" >
109-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:98:13-66
110            <intent-filter>
110-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:99:13-101:29
111                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
111-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:100:17-81
111-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:100:25-79
112            </intent-filter>
113        </receiver>
114        <receiver
114-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:104:9-117:20
115            android:name="com.bm.atool.receivers.WakeUpReceiver"
115-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-53
116            android:exported="true"
116-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:107:13-36
117            android:process=":watch" >
117-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-37
118            <intent-filter>
118-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:108:13-116:29
119                <action android:name="android.intent.action.USER_PRESENT" />
119-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-76
119-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-74
120                <action android:name="android.intent.action.BOOT_COMPLETED" />
120-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
120-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
121                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
121-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:111:17-79
121-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:111:25-76
122                <action android:name="android.intent.action.USER_PRESENT" />
122-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-76
122-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-74
123                <action android:name="android.intent.action.MEDIA_MOUNTED" />
123-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:17-78
123-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:25-75
124                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
124-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-87
124-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-84
125                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
125-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:115:17-90
125-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:115:25-87
126            </intent-filter>
127        </receiver>
128        <receiver
128-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:118:9-145:20
129            android:name="com.bm.atool.receivers.WakeUpAutoStartReceiver"
129-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-62
130            android:exported="true"
130-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:121:13-36
131            android:process=":watch" >
131-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:120:13-37
132
133            <!-- 手机启动 -->
134            <intent-filter>
134-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:123:13-126:29
135                <action android:name="android.intent.action.BOOT_COMPLETED" />
135-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
135-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
136                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
136-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:111:17-79
136-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:111:25-76
137            </intent-filter>
138            <!-- 软件安装卸载 -->
139            <intent-filter>
139-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:128:13-132:29
140                <action android:name="android.intent.action.PACKAGE_ADDED" />
140-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:129:17-77
140-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:129:25-75
141                <action android:name="android.intent.action.PACKAGE_REMOVED" />
141-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-79
141-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:130:25-77
142
143                <data android:scheme="package" />
143-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:131:17-49
143-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:131:23-47
144            </intent-filter>
145            <!-- 网络监听 -->
146            <intent-filter>
146-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:134:13-138:29
147                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
147-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:111:17-79
147-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:111:25-76
148                <action android:name="android.net.wifi.WIFI_STATE_CJANGED" />
148-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:17-77
148-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:136:25-75
149                <action android:name="android.net.wifi.STATE_CHANGE" />
149-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:137:17-71
149-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:137:25-69
150            </intent-filter>
151            <!-- 文件挂载 -->
152            <intent-filter>
152-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:140:13-144:29
153                <action android:name="android.intent.action.MEDIA_EJECT" />
153-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:141:17-75
153-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:141:25-73
154                <action android:name="android.intent.action.MEDIA_MOUNTED" />
154-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:17-78
154-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:113:25-75
155
156                <data android:scheme="file" />
156-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:131:17-49
156-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:131:23-47
157            </intent-filter>
158        </receiver>
159
160        <!-- 守护进程 watch -->
161        <service
161-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:148:9-153:43
162            android:name="com.bm.atool.service.JobSchedulerService"
162-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-56
163            android:enabled="true"
163-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:151:13-35
164            android:exported="true"
164-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:152:13-36
165            android:permission="android.permission.BIND_JOB_SERVICE"
165-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:150:13-69
166            android:process=":watch_job" />
166-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:153:13-41
167        <service
167-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:155:9-160:43
168            android:name="com.bm.atool.service.WatchDogService"
168-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:156:13-52
169            android:enabled="true"
169-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:158:13-35
170            android:exported="true"
170-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:159:13-36
171            android:foregroundServiceType="mediaPlayback"
171-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:157:13-58
172            android:process=":watch_dog" />
172-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:160:13-41
173        <service
173-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:162:9-164:46
174            android:name="com.bm.atool.service.PlayMusicService"
174-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:162:18-58
175            android:foregroundServiceType="mediaPlayback"
175-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:163:13-58
176            android:process=":watch_player" />
176-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:164:13-44
177        <service
177-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:165:9-178:19
178            android:name="com.bm.atool.service.ANTAccessibilityService"
178-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:166:13-60
179            android:enabled="true"
179-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:167:13-35
180            android:exported="true"
180-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:168:13-36
181            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
181-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:170:13-79
182            android:process=":accessibility" >
182-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:169:13-45
183            <intent-filter>
183-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:171:13-173:29
184                <action android:name="android.accessibilityservice.AccessibilityService" />
184-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:172:17-92
184-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:172:25-89
185            </intent-filter>
186
187            <meta-data
187-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:175:13-177:54
188                android:name="android.accessibilityservice"
188-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:176:17-60
189                android:resource="@xml/allocation" />
189-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:177:17-51
190        </service>
191        <service
191-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:179:9-195:19
192            android:name="com.bm.atool.service.SocketService"
192-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:180:13-50
193            android:exported="false"
193-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:181:13-37
194            android:label="SocketService"
194-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:182:13-42
195            android:permission="android.permission.BIND_VPN_SERVICE"
195-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:184:13-69
196            android:process=":socket" >
196-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:183:13-38
197            <intent-filter>
197-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:185:13-187:29
198                <action android:name="android.net.VpnService" />
198-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:186:17-65
198-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:186:25-62
199            </intent-filter>
200
201            <meta-data
201-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:188:13-190:39
202                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
202-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:189:17-73
203                android:value="true" />
203-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:190:17-37
204
205            <property
205-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:191:13-193:38
206                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
206-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:192:17-76
207                android:value="vpn" />
207-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:193:17-36
208        </service>
209        <service
209-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:197:9-205:19
210            android:name="com.bm.atool.service.NotificationService"
210-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:198:13-56
211            android:exported="true"
211-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:201:13-36
212            android:label="@string/app_name"
212-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:199:13-45
213            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
213-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:200:13-87
214            <intent-filter>
214-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:202:13-204:29
215                <action android:name="android.service.notification.NotificationListenerService" />
215-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:203:17-99
215-->F:\work2025\shanghai\android-tool-v2\android-tool-v2\app\src\main\AndroidManifest.xml:203:25-96
216            </intent-filter>
217        </service>
218
219        <!-- AutoJs6 Accessibility Service - 暂时注释掉 -->
220        <!--
221        <service
222            android:name=".service.AutoJs6AccessibilityService"
223            android:enabled="true"
224            android:exported="true"
225            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
226            <intent-filter>
227                <action android:name="android.accessibilityservice.AccessibilityService" />
228            </intent-filter>
229            <meta-data
230                android:name="android.accessibilityservice"
231                android:resource="@xml/autojs_accessibility_service" />
232        </service>
233        -->
234
235        <provider
235-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
236            android:name="androidx.startup.InitializationProvider"
236-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
237            android:authorities="com.bm.atool.androidx-startup"
237-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
238            android:exported="false" >
238-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
239            <meta-data
239-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
240                android:name="androidx.emoji2.text.EmojiCompatInitializer"
240-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
241                android:value="androidx.startup" />
241-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\b14b16a724c4b2c303f1de2a629a11a1\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
242            <meta-data
242-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
243                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
243-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
244                android:value="androidx.startup" />
244-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\e202377116353f3d11d21bbd87a04924\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
245            <meta-data
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
246                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
247                android:value="androidx.startup" />
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
248        </provider>
249
250        <uses-library
250-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
251            android:name="androidx.window.extensions"
251-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
252            android:required="false" />
252-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
253        <uses-library
253-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
254            android:name="androidx.window.sidecar"
254-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
255            android:required="false" />
255-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fe3fe22f92817277ba29d2ffd5ac05f6\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
256
257        <receiver
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
258            android:name="androidx.profileinstaller.ProfileInstallReceiver"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
259            android:directBootAware="false"
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
260            android:enabled="true"
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
261            android:exported="true"
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
262            android:permission="android.permission.DUMP" >
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
264                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
265            </intent-filter>
266            <intent-filter>
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
267                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
268            </intent-filter>
269            <intent-filter>
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
270                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
271            </intent-filter>
272            <intent-filter>
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
273                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\939611af08d6334e6a6719548d0fc7eb\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
274            </intent-filter>
275        </receiver>
276    </application>
277
278</manifest>
