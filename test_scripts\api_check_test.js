// API检查测试脚本
console.log("API检查测试开始");

// 检查API是否已加载
if (typeof window.autoJsReady !== 'undefined' && window.autoJsReady) {
    console.log("API已正确加载");
    toast("API检查通过!");
    
    // 检查各个功能是否可用
    console.log("toast功能: " + (typeof toast));
    console.log("device对象: " + (typeof device));
    console.log("currentPackage功能: " + (typeof currentPackage));
    
    // 测试基本功能
    toast("API功能测试成功!");
} else {
    console.error("API未正确加载");
    var availableGlobals = Object.keys(window).filter(function(k) { 
        return k !== 'webkit' && k !== 'chrome'; 
    }).join(', ');
    console.error("可用的全局对象: " + availableGlobals);
    toast("API未正确加载");
}